"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRout<PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { UserLevel, ModuleType } from "@prisma/client"
import { Target, Clock, BookOpen, Award, CheckCircle, Circle, Play, Brain } from "lucide-react"
import Link from "next/link"

interface Roadmap {
  id: string
  title: string
  description: string
  targetLevel: UserLevel
  timeCommitment: number
  estimatedWeeks: number
  createdAt: string
  modules: Array<{
    id: string
    title: string
    description: string
    type: ModuleType
    estimatedDays: number
    order: number
    milestones: Array<{
      id: string
      title: string
      description: string
      achieved: boolean
    }>
    lessons?: Array<{
      id: string
      title: string
      description: string
      order: number
      progress?: Array<{
        status: string
        progress: number
      }>
    }>
    progress?: Array<{
      status: string
      progress: number
    }>
  }>
}

export default function Dashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatingLessons, setGeneratingLessons] = useState<{[key: string]: boolean}>({})
  const [roadmaps, setRoadmaps] = useState<Roadmap[]>([])
  const [error, setError] = useState("")
  const [selectedTargetLevel, setSelectedTargetLevel] = useState<UserLevel | "">("")

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
    }
  }, [status, router])

  useEffect(() => {
    if (session) {
      fetchRoadmaps()
    }
  }, [session])

  const fetchRoadmaps = async () => {
    try {
      const response = await fetch("/api/roadmaps")
      if (response.ok) {
        const data = await response.json()
        setRoadmaps(data.roadmaps)
      }
    } catch (error) {
      console.error("Error fetching roadmaps:", error)
    }
  }

  const generateRoadmap = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsGenerating(true)
    setError("")

    const formData = new FormData(e.currentTarget)
    const targetLevel = selectedTargetLevel as UserLevel
    const learningGoals = formData.get("learningGoals") as string
    const timeCommitment = formData.get("timeCommitment") as string

    // Validate that target level is selected
    if (!targetLevel) {
      setError("Please select a target level")
      setIsGenerating(false)
      return
    }

    try {
      const response = await fetch("/api/roadmaps/generate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ targetLevel, learningGoals, timeCommitment })
      })

      if (response.ok) {
        const data = await response.json()
        setRoadmaps(prev => [data.roadmap, ...prev])
        // Reset form
        e.currentTarget.reset()
        setSelectedTargetLevel("")
      } else {
        const errorData = await response.json()
        setError(errorData.error || "Failed to generate roadmap")
      }
    } catch (error) {
      setError("An error occurred. Please try again.")
    } finally {
      setIsGenerating(false)
    }
  }

  const generateLessonsForModule = async (moduleId: string, moduleTitle: string, moduleType: ModuleType, moduleDescription: string, order: number) => {
    setGeneratingLessons(prev => ({ ...prev, [moduleId]: true }))
    
    try {
      const response = await fetch("/api/lessons/generate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ moduleId, moduleTitle, moduleType, moduleDescription, order })
      })

      if (response.ok) {
        // Refresh roadmaps to show new lessons
        await fetchRoadmaps()
      } else {
        const errorData = await response.json()
        setError(errorData.error || "Failed to generate lessons")
      }
    } catch (error) {
      setError("An error occurred. Please try again.")
    } finally {
      setGeneratingLessons(prev => ({ ...prev, [moduleId]: false }))
    }
  }

  const getModuleTypeColor = (type: ModuleType) => {
    const colors = {
      HIRAGANA: "bg-blue-100 text-blue-800",
      KATAKANA: "bg-green-100 text-green-800",
      VOCABULARY: "bg-purple-100 text-purple-800",
      GRAMMAR: "bg-orange-100 text-orange-800",
      KANJI: "bg-red-100 text-red-800",
      CONVERSATION: "bg-pink-100 text-pink-800",
      LISTENING: "bg-indigo-100 text-indigo-800",
      READING: "bg-yellow-100 text-yellow-800",
      WRITING: "bg-gray-100 text-gray-800",
      REVIEW: "bg-teal-100 text-teal-800"
    }
    return colors[type] || "bg-gray-100 text-gray-800"
  }

  const calculateRoadmapProgress = (roadmap: Roadmap) => {
    if (!roadmap.modules.length) return 0
    
    const totalModules = roadmap.modules.length
    const completedModules = roadmap.modules.filter(module => 
      module.progress?.some(p => p.status === "COMPLETED")
    ).length
    
    return (completedModules / totalModules) * 100
  }

  const calculateModuleProgress = (module: Roadmap['modules'][0]) => {
    if (!module.lessons || module.lessons.length === 0) return 0
    
    const completedLessons = module.lessons.filter(lesson => 
      lesson.progress?.some(p => p.status === "COMPLETED")
    ).length
    
    return (completedLessons / module.lessons.length) * 100
  }

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-gray-600">Welcome back, {session.user.name || session.user.email}!</p>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="outline">
                {session.user.level}
              </Badge>
              <Badge variant="outline">
                <Clock className="w-4 h-4 mr-1" />
                {session.user.timeCommitment} min/day
              </Badge>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="roadmaps" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="roadmaps">My Roadmaps</TabsTrigger>
            <TabsTrigger value="review">Review</TabsTrigger>
            <TabsTrigger value="generate">Generate New</TabsTrigger>
          </TabsList>
          
          <TabsContent value="roadmaps" className="space-y-6">
            <div className="grid gap-6">
              {roadmaps.length === 0 ? (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <Target className="w-12 h-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No roadmaps yet</h3>
                    <p className="text-gray-600 text-center mb-4">
                      Generate your first personalized Japanese learning roadmap
                    </p>
                    <Button onClick={() => router.push("/dashboard?tab=generate")}>
                      Generate Roadmap
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                roadmaps.map((roadmap) => (
                  <Card key={roadmap.id}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-xl">{roadmap.title}</CardTitle>
                          <CardDescription>{roadmap.description}</CardDescription>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">{roadmap.targetLevel}</Badge>
                          <Badge variant="outline">
                            <Clock className="w-4 h-4 mr-1" />
                            {roadmap.estimatedWeeks} weeks
                          </Badge>
                        </div>
                      </div>
                      <div className="mt-4">
                        <div className="flex justify-between text-sm text-gray-600 mb-2">
                          <span>Progress</span>
                          <span>{Math.round(calculateRoadmapProgress(roadmap))}%</span>
                        </div>
                        <Progress value={calculateRoadmapProgress(roadmap)} className="h-2" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <h4 className="font-semibold text-gray-900">Modules</h4>
                        <div className="grid gap-3">
                          {roadmap.modules.map((module) => (
                            <div key={module.id} className="border rounded-lg p-4">
                              <div className="flex justify-between items-start mb-2">
                                <div className="flex items-center space-x-2">
                                  <Badge className={getModuleTypeColor(module.type)}>
                                    {module.type}
                                  </Badge>
                                  <h5 className="font-medium">{module.title}</h5>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm text-gray-600">
                                    {module.estimatedDays} days
                                  </span>
                                  {module.progress?.some(p => p.status === "COMPLETED") ? (
                                    <CheckCircle className="w-5 h-5 text-green-600" />
                                  ) : (
                                    <Circle className="w-5 h-5 text-gray-400" />
                                  )}
                                </div>
                              </div>
                              <p className="text-sm text-gray-600 mb-3">{module.description}</p>
                              
                              {/* Module Progress */}
                              {module.lessons && module.lessons.length > 0 && (
                                <div className="mb-3">
                                  <div className="flex justify-between text-xs text-gray-600 mb-1">
                                    <span>Progress</span>
                                    <span>{Math.round(calculateModuleProgress(module))}%</span>
                                  </div>
                                  <Progress value={calculateModuleProgress(module)} className="h-1" />
                                </div>
                              )}
                              
                              {/* Lessons Section */}
                              <div className="mb-3">
                                <div className="flex justify-between items-center mb-2">
                                  <h6 className="text-sm font-medium">Lessons</h6>
                                  {!module.lessons || module.lessons.length === 0 ? (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => generateLessonsForModule(
                                        module.id, 
                                        module.title, 
                                        module.type, 
                                        module.description, 
                                        module.order
                                      )}
                                      disabled={generatingLessons[module.id]}
                                    >
                                      {generatingLessons[module.id] ? "Generating..." : "Generate Lessons"}
                                    </Button>
                                  ) : (
                                    <Badge variant="secondary">
                                      {module.lessons.length} lesson{module.lessons.length !== 1 ? 's' : ''}
                                    </Badge>
                                  )}
                                </div>
                                
                                {module.lessons && module.lessons.length > 0 && (
                                  <div className="space-y-2">
                                    {module.lessons.map((lesson) => (
                                      <div key={lesson.id} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                                        <div className="flex items-center space-x-2">
                                          <span className="text-sm font-medium">{lesson.title}</span>
                                          {lesson.progress?.some(p => p.status === "COMPLETED") ? (
                                            <CheckCircle className="w-4 h-4 text-green-600" />
                                          ) : (
                                            <Circle className="w-4 h-4 text-gray-400" />
                                          )}
                                        </div>
                                        <Link href={`/lessons/${lesson.id}`}>
                                          <Button size="sm" variant="ghost">
                                            <Play className="w-4 h-4" />
                                          </Button>
                                        </Link>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>
                              
                              {module.milestones.length > 0 && (
                                <div className="flex flex-wrap gap-2">
                                  {module.milestones.map((milestone) => (
                                    <Badge
                                      key={milestone.id}
                                      variant={milestone.achieved ? "default" : "secondary"}
                                      className="text-xs"
                                    >
                                      {milestone.achieved ? "✓" : "○"} {milestone.title}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="review" className="space-y-6">
            <div className="grid gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Brain className="w-5 h-5 text-purple-600" />
                    <span>Spaced Repetition Review</span>
                  </CardTitle>
                  <CardDescription>
                    Review items at optimal intervals to maximize retention
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-3 gap-4 mb-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">0</div>
                      <div className="text-sm text-gray-600">Due Today</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">0</div>
                      <div className="text-sm text-gray-600">This Week</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">0</div>
                      <div className="text-sm text-gray-600">Total Items</div>
                    </div>
                  </div>
                  <Link href="/review">
                    <Button className="w-full">
                      Start Review Session
                    </Button>
                  </Link>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>How Spaced Repetition Works</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <h4 className="font-medium">🧠 Memory Science</h4>
                      <p className="text-sm text-gray-600">
                        Reviews are scheduled at optimal intervals based on cognitive science research.
                      </p>
                    </div>
                    <div className="space-y-2">
                      <h4 className="font-medium">📈 Adaptive Scheduling</h4>
                      <p className="text-sm text-gray-600">
                        Easy items appear less frequently, challenging items more often.
                      </p>
                    </div>
                    <div className="space-y-2">
                      <h4 className="font-medium">⚡ Efficient Learning</h4>
                      <p className="text-sm text-gray-600">
                        Maximize retention with minimum time investment.
                      </p>
                    </div>
                    <div className="space-y-2">
                      <h4 className="font-medium">🎯 Personalized</h4>
                      <p className="text-sm text-gray-600">
                        Adapts to your personal learning pace and performance.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="generate" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Generate New Roadmap</CardTitle>
                <CardDescription>
                  Create a personalized learning roadmap based on your goals and preferences
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={generateRoadmap} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="targetLevel">Target Level</Label>
                      <Select value={selectedTargetLevel} onValueChange={(value) => setSelectedTargetLevel(value as UserLevel)} required>
                        <SelectTrigger>
                          <SelectValue placeholder="Select your target level" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="BEGINNER">Beginner</SelectItem>
                          <SelectItem value="INTERMEDIATE">Intermediate</SelectItem>
                          <SelectItem value="ADVANCED">Advanced</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="timeCommitment">Daily Time Commitment (minutes)</Label>
                      <Input
                        id="timeCommitment"
                        name="timeCommitment"
                        type="number"
                        placeholder="30"
                        defaultValue="30"
                        min="5"
                        max="240"
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="learningGoals">Learning Goals</Label>
                    <Textarea
                      id="learningGoals"
                      name="learningGoals"
                      placeholder="e.g., Pass JLPT N5, Daily conversation, Business Japanese, Read manga"
                      defaultValue="General Japanese learning"
                      required
                    />
                  </div>
                  
                  {error && (
                    <div className="text-red-600 text-sm">{error}</div>
                  )}
                  
                  <Button type="submit" disabled={isGenerating} className="w-full">
                    {isGenerating ? "Generating Roadmap..." : "Generate Roadmap"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}